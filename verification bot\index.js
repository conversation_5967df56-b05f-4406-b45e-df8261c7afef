const { Client, GatewayIntentBits, SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const config = require('./config.js');

// إنشاء العميل
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildMessages
    ]
});

// عند تشغيل البوت
client.once('ready', async () => {
    console.log(`✅ البوت جاهز! تم تسجيل الدخول باسم ${client.user.tag}`);
    
    // تسجيل الأوامر
    await registerCommands();
});

// تسجيل الأوامر
async function registerCommands() {
    const commands = [
        new SlashCommandBuilder()
            .setName('verification')
            .setDescription('تحقق من عضو جديد')
            .addStringOption(option =>
                option.setName('gender')
                    .setDescription('جنس العضو')
                    .setRequired(true)
                    .addChoices(
                        { name: 'Male', value: 'male' },
                        { name: 'Female', value: 'female' }
                    )
            )
            .addUserOption(option =>
                option.setName('user')
                    .setDescription('العضو المراد التحقق منه')
                    .setRequired(true)
            ),
        new SlashCommandBuilder()
            .setName('unverify')
            .setDescription('إلغاء تحقق عضو')
            .addUserOption(option =>
                option.setName('user')
                    .setDescription('العضو المراد إلغاء تحققه')
                    .setRequired(true)
            )
    ];

    try {
        console.log('🔄 بدء تسجيل الأوامر...');
        
        if (config.guildId && config.guildId !== 'YOUR_GUILD_ID_HERE') {
            // تسجيل للسيرفر المحدد فقط (أسرع)
            const guild = client.guilds.cache.get(config.guildId);
            if (guild) {
                await guild.commands.set(commands);
                console.log('✅ تم تسجيل الأوامر للسيرفر المحدد');
            }
        } else {
            // تسجيل عالمي (يحتاج وقت أكثر)
            await client.application.commands.set(commands);
            console.log('✅ تم تسجيل الأوامر عالمياً');
        }
    } catch (error) {
        console.error('❌ خطأ في تسجيل الأوامر:', error);
    }
}

// التعامل مع انضمام عضو جديد
client.on('guildMemberAdd', async member => {
    try {
        const welcomeChannel = member.guild.channels.cache.get(config.welcomeChannel);
        if (!welcomeChannel) return;

        const verificationStaffRole = member.guild.roles.cache.get(config.roles.verificationStaff);

        const welcomeEmbed = new EmbedBuilder()
            .setTitle('🎉 Welcome to the Server!')
            .setDescription(`Welcome ${member}! Please follow these steps to get verified:`)
            .setColor('#00ff00')
            .addFields(
                {
                    name: '📋 Verification Steps:',
                    value: `1️⃣ Go to <#${config.welcomeChannel}>\n2️⃣ Mention ${verificationStaffRole ? verificationStaffRole : '@Verification Staff'}\n3️⃣ Wait for staff response\n4️⃣ Follow staff instructions`,
                    inline: false
                },
                {
                    name: '⚠️ Important:',
                    value: 'Please be patient and wait for verification staff to assist you.',
                    inline: false
                }
            )
            .setThumbnail(member.user.displayAvatarURL())
            .setTimestamp()
            .setFooter({
                text: 'Verification System',
                iconURL: member.guild.iconURL()
            });

        await welcomeChannel.send({
            content: `${member} ${verificationStaffRole ? verificationStaffRole : ''}`,
            embeds: [welcomeEmbed]
        });

    } catch (error) {
        console.error('خطأ في إرسال رسالة الترحيب:', error);
    }
});

// التعامل مع التفاعلات
client.on('interactionCreate', async interaction => {
    if (!interaction.isChatInputCommand()) return;

    if (interaction.commandName === 'verification') {
        // التحقق من رول مسؤولي التحقق قبل معالجة الأمر
        if (!interaction.member.roles.cache.has(config.roles.verificationStaff)) {
            // لا نرسل أي رد - الأمر سيبدو وكأنه لم يحدث شيء
            return;
        }
        await handleVerification(interaction);
    }

    if (interaction.commandName === 'unverify') {
        // التحقق من رول مسؤولي التحقق قبل معالجة الأمر
        if (!interaction.member.roles.cache.has(config.roles.verificationStaff)) {
            // لا نرسل أي رد - الأمر سيبدو وكأنه لم يحدث شيء
            return;
        }
        await handleUnverification(interaction);
    }
});

// دالة التحقق
async function handleVerification(interaction) {
    try {
        // الحصول على البيانات
        const gender = interaction.options.getString('gender');
        const targetUser = interaction.options.getUser('user');
        const member = interaction.guild.members.cache.get(targetUser.id);

        if (!member) {
            return await interaction.reply({
                content: '❌ Member not found in the server',
                ephemeral: true
            });
        }

        await interaction.deferReply({ ephemeral: true });

        // الحصول على الأدوار
        const verifiedRole = interaction.guild.roles.cache.get(config.roles.verified);
        const genderRole = interaction.guild.roles.cache.get(
            gender === 'male' ? config.roles.male : config.roles.female
        );
        const unverifiedRole = interaction.guild.roles.cache.get(config.roles.unverified);

        // التحقق إذا كان العضو متحقق مسبقاً
        if (member.roles.cache.has(config.roles.verified)) {
            let verificationGender = 'Unknown';

            // تحديد الجنس المحقق مسبقاً
            if (member.roles.cache.has(config.roles.male)) {
                verificationGender = 'Male';
            } else if (member.roles.cache.has(config.roles.female)) {
                verificationGender = 'Female';
            }

            return await interaction.editReply({
                content: `❌ Already verified as ${verificationGender}`,
                ephemeral: true
            });
        }

        // التحقق من وجود الأدوار
        if (!verifiedRole) {
            return await interaction.editReply('❌ رول التحقق غير موجود');
        }
        if (!genderRole) {
            return await interaction.editReply('❌ رول الجنس غير موجود');
        }

        // إزالة رول غير المتحقق إذا كان موجود
        if (unverifiedRole && member.roles.cache.has(unverifiedRole.id)) {
            await member.roles.remove(unverifiedRole);
        }

        // إضافة الأدوار الجديدة
        await member.roles.add([verifiedRole, genderRole]);

        // لا نحتاج لتحديث اسم العضو بعد الآن

        // إنشاء رسالة التأكيد (مرئية فقط للمستخدم)
        const confirmEmbed = new EmbedBuilder()
            .setTitle('✅ Verification Completed')
            .setColor('#00ff00')
            .setDescription('The member has been successfully verified.')
            .setTimestamp();

        await interaction.editReply({ embeds: [confirmEmbed] });

        // إرسال لوق مفصل في قناة اللوق
        const logChannel = interaction.guild.channels.cache.get(config.verificationLogChannel);
        if (logChannel) {
            // محاولة الحصول على معلومات الدعوة
            let inviteInfo = 'Unknown';
            try {
                const invites = await interaction.guild.invites.fetch();
                const memberInvite = invites.find(invite => invite.uses > 0);
                if (memberInvite && memberInvite.inviter) {
                    inviteInfo = `${memberInvite.inviter.tag} (${memberInvite.code})`;
                }
            } catch (error) {
                console.log('تعذر الحصول على معلومات الدعوة:', error.message);
            }

            const logEmbed = new EmbedBuilder()
                .setTitle('🔍 Verification Log')
                .setColor('#00ff00')
                .addFields(
                    { name: '👤 Member', value: `${targetUser} (${targetUser.tag})`, inline: true },
                    { name: '🆔 User ID', value: targetUser.id, inline: true },
                    { name: '⚧️ Gender', value: gender === 'male' ? 'Male' : 'Female', inline: true },
                    { name: '🎭 Roles Added', value: `${verifiedRole}\n${genderRole}`, inline: false },
                    { name: '👮 Verified By', value: `${interaction.user} (${interaction.user.tag})`, inline: true },
                    { name: '📅 Verification Time', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true },
                    { name: '📊 Account Created', value: `<t:${Math.floor(targetUser.createdTimestamp / 1000)}:R>`, inline: true },
                    { name: '🚪 Joined Server', value: `<t:${Math.floor(member.joinedTimestamp / 1000)}:R>`, inline: true },
                    { name: '🔗 Invited By', value: inviteInfo, inline: true }
                )
                .setTimestamp()
                .setThumbnail(targetUser.displayAvatarURL())
                .setFooter({
                    text: `Verification System`,
                    iconURL: interaction.guild.iconURL()
                });

            if (unverifiedRole && member.roles.cache.has(unverifiedRole.id)) {
                logEmbed.addFields({
                    name: '🗑️ Roles Removed',
                    value: `${unverifiedRole}`,
                    inline: false
                });
            }

            try {
                await logChannel.send({ embeds: [logEmbed] });
            } catch (error) {
                console.log('تعذر إرسال اللوق إلى القناة المحددة:', error.message);
            }
        }

        // إرسال رسالة ترحيب للعضو (اختياري)
        try {
            const welcomeEmbed = new EmbedBuilder()
                .setTitle('🎉 Welcome!')
                .setDescription(`Hello ${targetUser.username}! Your account has been successfully verified in the server.`)
                .setColor('#00ff00')
                .setTimestamp();

            await targetUser.send({ embeds: [welcomeEmbed] });
        } catch (error) {
            console.log('تعذر إرسال رسالة ترحيب خاصة للعضو');
        }

    } catch (error) {
        console.error('خطأ في عملية التحقق:', error);
        
        const errorMessage = { content: '❌ An error occurred during verification', ephemeral: true };
            
        if (interaction.replied || interaction.deferred) {
            await interaction.editReply(errorMessage);
        } else {
            await interaction.reply(errorMessage);
        }
    }
}

// دالة إلغاء التحقق
async function handleUnverification(interaction) {
    try {
        // الحصول على البيانات
        const targetUser = interaction.options.getUser('user');
        const member = interaction.guild.members.cache.get(targetUser.id);

        if (!member) {
            return await interaction.reply({
                content: '❌ Member not found in the server',
                ephemeral: true
            });
        }

        await interaction.deferReply({ ephemeral: true });

        // الحصول على الأدوار
        const verifiedRole = interaction.guild.roles.cache.get(config.roles.verified);
        const maleRole = interaction.guild.roles.cache.get(config.roles.male);
        const femaleRole = interaction.guild.roles.cache.get(config.roles.female);
        const unverifiedRole = interaction.guild.roles.cache.get(config.roles.unverified);

        // التحقق إذا كان العضو غير متحقق أصلاً
        if (!member.roles.cache.has(config.roles.verified)) {
            return await interaction.editReply({
                content: `❌ Member is not verified`
            });
        }

        // تحديد الجنس المحقق حالياً
        let currentGender = 'Unknown';
        if (member.roles.cache.has(config.roles.male)) {
            currentGender = 'Male';
        } else if (member.roles.cache.has(config.roles.female)) {
            currentGender = 'Female';
        }

        // إزالة أدوار التحقق
        const rolesToRemove = [];
        if (verifiedRole && member.roles.cache.has(verifiedRole.id)) {
            rolesToRemove.push(verifiedRole);
        }
        if (maleRole && member.roles.cache.has(maleRole.id)) {
            rolesToRemove.push(maleRole);
        }
        if (femaleRole && member.roles.cache.has(femaleRole.id)) {
            rolesToRemove.push(femaleRole);
        }

        if (rolesToRemove.length > 0) {
            await member.roles.remove(rolesToRemove);
        }

        // إضافة رول غير المتحقق إذا كان موجود
        if (unverifiedRole) {
            await member.roles.add(unverifiedRole);
        }

        // إنشاء رسالة التأكيد (مرئية فقط للمستخدم)
        const confirmEmbed = new EmbedBuilder()
            .setTitle('✅ Unverification Completed')
            .setColor('#ff0000')
            .setDescription('The member has been successfully unverified.')
            .setTimestamp();

        await interaction.editReply({ embeds: [confirmEmbed] });

        // إرسال لوق مفصل في قناة اللوق
        const logChannel = interaction.guild.channels.cache.get(config.verificationLogChannel);
        if (logChannel) {
            const logEmbed = new EmbedBuilder()
                .setTitle('🚫 Unverification Log')
                .setColor('#ff0000')
                .addFields(
                    { name: '👤 Member', value: `${targetUser} (${targetUser.tag})`, inline: true },
                    { name: '🆔 User ID', value: targetUser.id, inline: true },
                    { name: '⚧️ Previous Gender', value: currentGender, inline: true },
                    { name: '🗑️ Roles Removed', value: rolesToRemove.map(role => role.toString()).join('\n') || 'None', inline: false },
                    { name: '👮 Unverified By', value: `${interaction.user} (${interaction.user.tag})`, inline: true },
                    { name: '📅 Unverification Time', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
                )
                .setTimestamp()
                .setThumbnail(targetUser.displayAvatarURL())
                .setFooter({
                    text: `Verification System`,
                    iconURL: interaction.guild.iconURL()
                });

            if (unverifiedRole) {
                logEmbed.addFields({
                    name: '🎭 Roles Added',
                    value: `${unverifiedRole}`,
                    inline: false
                });
            }

            try {
                await logChannel.send({ embeds: [logEmbed] });
            } catch (error) {
                console.log('تعذر إرسال اللوق إلى القناة المحددة:', error.message);
            }
        }

    } catch (error) {
        console.error('خطأ في عملية إلغاء التحقق:', error);

        try {
            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({ content: '❌ An error occurred during unverification' });
            } else {
                await interaction.reply({ content: '❌ An error occurred during unverification', ephemeral: true });
            }
        } catch (replyError) {
            console.error('خطأ في إرسال رسالة الخطأ:', replyError);
        }
    }
}

// تسجيل الدخول
client.login(config.token);