const { Client, GatewayIntentBits, SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const config = require('./config.js');

// إنشاء العميل
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildMessages
    ]
});

// عند تشغيل البوت
client.once('ready', async () => {
    console.log(`✅ البوت جاهز! تم تسجيل الدخول باسم ${client.user.tag}`);
    
    // تسجيل الأوامر
    await registerCommands();
});

// تسجيل الأوامر
async function registerCommands() {
    const commands = [
        new SlashCommandBuilder()
            .setName('verification')
            .setDescription('تحقق من عضو جديد')
            .addStringOption(option =>
                option.setName('gender')
                    .setDescription('جنس العضو')
                    .setRequired(true)
                    .addChoices(
                        { name: 'Male', value: 'male' },
                        { name: 'Female', value: 'female' }
                    )
            )
            .addUserOption(option =>
                option.setName('user')
                    .setDescription('العضو المراد التحقق منه')
                    .setRequired(true)
            )
    ];

    try {
        console.log('🔄 بدء تسجيل الأوامر...');
        
        if (config.guildId && config.guildId !== 'YOUR_GUILD_ID_HERE') {
            // تسجيل للسيرفر المحدد فقط (أسرع)
            const guild = client.guilds.cache.get(config.guildId);
            if (guild) {
                await guild.commands.set(commands);
                console.log('✅ تم تسجيل الأوامر للسيرفر المحدد');
            }
        } else {
            // تسجيل عالمي (يحتاج وقت أكثر)
            await client.application.commands.set(commands);
            console.log('✅ تم تسجيل الأوامر عالمياً');
        }
    } catch (error) {
        console.error('❌ خطأ في تسجيل الأوامر:', error);
    }
}

// التعامل مع التفاعلات
client.on('interactionCreate', async interaction => {
    if (!interaction.isChatInputCommand()) return;

    if (interaction.commandName === 'verification') {
        // التحقق من رول مسؤولي التحقق قبل معالجة الأمر
        if (!interaction.member.roles.cache.has(config.roles.verificationStaff)) {
            // لا نرسل أي رد - الأمر سيبدو وكأنه لم يحدث شيء
            return;
        }
        await handleVerification(interaction);
    }
});

// دالة التحقق
async function handleVerification(interaction) {
    try {
        // الحصول على البيانات
        const gender = interaction.options.getString('gender');
        const targetUser = interaction.options.getUser('user');
        const member = interaction.guild.members.cache.get(targetUser.id);

        if (!member) {
            return await interaction.reply({
                content: '❌ لم يتم العثور على العضو في السيرفر',
                ephemeral: true
            });
        }



        await interaction.deferReply();

        // الحصول على الأدوار
        const verifiedRole = interaction.guild.roles.cache.get(config.roles.verified);
        const genderRole = interaction.guild.roles.cache.get(
            gender === 'male' ? config.roles.male : config.roles.female
        );
        const unverifiedRole = interaction.guild.roles.cache.get(config.roles.unverified);

        // التحقق من وجود الأدوار
        if (!verifiedRole) {
            return await interaction.editReply('❌ رول التحقق غير موجود');
        }
        if (!genderRole) {
            return await interaction.editReply('❌ رول الجنس غير موجود');
        }

        // إزالة رول غير المتحقق إذا كان موجود
        if (unverifiedRole && member.roles.cache.has(unverifiedRole.id)) {
            await member.roles.remove(unverifiedRole);
        }

        // إضافة الأدوار الجديدة
        await member.roles.add([verifiedRole, genderRole]);

        // لا نحتاج لتحديث اسم العضو بعد الآن

        // إنشاء رسالة التأكيد
        const embed = new EmbedBuilder()
            .setTitle('✅ تم التحقق بنجاح')
            .setColor('#00ff00')
            .addFields(
                { name: '👤 العضو', value: `${targetUser}`, inline: true },
                { name: '⚧️ الجنس', value: gender === 'male' ? 'Male' : 'Female', inline: true }
            )
            .addFields(
                { name: '🎭 الأدوار المضافة', value: `${verifiedRole}\n${genderRole}`, inline: false }
            )
            .setTimestamp()
            .setFooter({ 
                text: `تم بواسطة ${interaction.user.tag}`, 
                iconURL: interaction.user.displayAvatarURL() 
            });

        if (unverifiedRole && member.roles.cache.has(unverifiedRole.id)) {
            embed.addFields({ 
                name: '🗑️ الأدوار المحذوفة', 
                value: `${unverifiedRole}`, 
                inline: false 
            });
        }

        await interaction.editReply({ embeds: [embed] });

        // إرسال رسالة ترحيب للعضو (اختياري)
        try {
            const welcomeEmbed = new EmbedBuilder()
                .setTitle('🎉 مرحباً بك!')
                .setDescription(`مرحباً ${targetUser.username}! تم التحقق من حسابك بنجاح في السيرفر.`)
                .setColor('#00ff00')
                .setTimestamp();

            await targetUser.send({ embeds: [welcomeEmbed] });
        } catch (error) {
            console.log('تعذر إرسال رسالة ترحيب خاصة للعضو');
        }

    } catch (error) {
        console.error('خطأ في عملية التحقق:', error);
        
        const errorMessage = interaction.replied || interaction.deferred 
            ? { content: '❌ حدث خطأ أثناء عملية التحقق', ephemeral: true }
            : { content: '❌ حدث خطأ أثناء عملية التحقق', ephemeral: true };
            
        if (interaction.replied || interaction.deferred) {
            await interaction.editReply(errorMessage);
        } else {
            await interaction.reply(errorMessage);
        }
    }
}

// تسجيل الدخول
client.login(config.token);