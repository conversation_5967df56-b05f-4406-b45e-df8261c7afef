const { Client, GatewayIntentBits, SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const config = require('./config.js');
const sqlite3 = require('sqlite3').verbose();

// إعداد قاعدة البيانات
const db = new sqlite3.Database('./voice_stats.db');

// إنشاء جدول إحصائيات الوقت الصوتي
db.serialize(() => {
    db.run(`CREATE TABLE IF NOT EXISTS voice_stats (
        user_id TEXT PRIMARY KEY,
        username TEXT,
        total_time INTEGER DEFAULT 0,
        join_time INTEGER DEFAULT 0
    )`);
});

// متغير لتخزين أوقات الانضمام المؤقتة
const voiceJoinTimes = new Map();

// إنشاء العميل
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.GuildVoiceStates
    ]
});

// دوال مساعدة لقاعدة البيانات
function updateUserStats(userId, username, timeSpent) {
    return new Promise((resolve, reject) => {
        db.run(`INSERT OR REPLACE INTO voice_stats (user_id, username, total_time)
                VALUES (?, ?, COALESCE((SELECT total_time FROM voice_stats WHERE user_id = ?), 0) + ?)`,
            [userId, username, userId, timeSpent], function(err) {
                if (err) reject(err);
                else resolve();
            });
    });
}

function getTopUsers(limit = 10) {
    return new Promise((resolve, reject) => {
        db.all(`SELECT user_id, username, total_time FROM voice_stats
                ORDER BY total_time DESC LIMIT ?`, [limit], (err, rows) => {
            if (err) reject(err);
            else resolve(rows);
        });
    });
}

function formatTime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
}

async function updateLeaderboard() {
    try {
        const channel = client.channels.cache.get(config.voiceStatsChannel);
        if (!channel) return;

        const topUsers = await getTopUsers(10);

        const embed = new EmbedBuilder()
            .setTitle(`🎤 ${config.serverName} - Voice Time Leaderboard`)
            .setColor('#00ff00')
            .setDescription('Top 10 members by voice channel time')
            .setTimestamp()
            .setFooter({
                text: `${config.serverName} - Voice Stats`,
                iconURL: channel.guild.iconURL() || config.serverLogo
            });

        if (topUsers.length === 0) {
            embed.addFields({ name: '📊 No Data', value: 'No voice activity recorded yet.', inline: false });
        } else {
            const leaderboardText = topUsers.map((user, index) => {
                const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;
                return `${medal} **${user.username}** - ${formatTime(user.total_time)}`;
            }).join('\n');

            embed.addFields({ name: '🏆 Leaderboard', value: leaderboardText, inline: false });
        }

        // البحث عن رسالة اللوحة الموجودة أو إنشاء واحدة جديدة
        const messages = await channel.messages.fetch({ limit: 10 });
        const existingMessage = messages.find(msg =>
            msg.author.id === client.user.id &&
            msg.embeds.length > 0 &&
            msg.embeds[0].title?.includes('Voice Time Leaderboard')
        );

        if (existingMessage) {
            await existingMessage.edit({ embeds: [embed] });
        } else {
            await channel.send({ embeds: [embed] });
        }
    } catch (error) {
        console.error('خطأ في تحديث لوحة الإحصائيات:', error);
    }
}

// عند تشغيل البوت
client.once('ready', async () => {
    console.log(`✅ البوت جاهز! تم تسجيل الدخول باسم ${client.user.tag}`);

    // تسجيل الأوامر
    await registerCommands();

    // تحديث لوحة الإحصائيات عند البدء
    await updateLeaderboard();

    // تحديث لوحة الإحصائيات كل 5 دقائق
    setInterval(updateLeaderboard, 5 * 60 * 1000);
});

// تسجيل الأوامر
async function registerCommands() {
    const commands = [
        new SlashCommandBuilder()
            .setName('verification')
            .setDescription('تحقق من عضو جديد')
            .addStringOption(option =>
                option.setName('gender')
                    .setDescription('جنس العضو')
                    .setRequired(true)
                    .addChoices(
                        { name: 'Male', value: 'male' },
                        { name: 'Female', value: 'female' }
                    )
            )
            .addUserOption(option =>
                option.setName('user')
                    .setDescription('العضو المراد التحقق منه')
                    .setRequired(true)
            ),
        new SlashCommandBuilder()
            .setName('unverify')
            .setDescription('إلغاء تحقق عضو')
            .addUserOption(option =>
                option.setName('user')
                    .setDescription('العضو المراد إلغاء تحققه')
                    .setRequired(true)
            )
    ];

    try {
        console.log('🔄 بدء تسجيل الأوامر...');
        
        if (config.guildId && config.guildId !== 'YOUR_GUILD_ID_HERE') {
            // تسجيل للسيرفر المحدد فقط (أسرع)
            const guild = client.guilds.cache.get(config.guildId);
            if (guild) {
                await guild.commands.set(commands);
                console.log('✅ تم تسجيل الأوامر للسيرفر المحدد');
            }
        } else {
            // تسجيل عالمي (يحتاج وقت أكثر)
            await client.application.commands.set(commands);
            console.log('✅ تم تسجيل الأوامر عالمياً');
        }
    } catch (error) {
        console.error('❌ خطأ في تسجيل الأوامر:', error);
    }
}

// التعامل مع تغييرات حالة الصوت
client.on('voiceStateUpdate', async (oldState, newState) => {
    try {
        const userId = newState.member.id;
        const username = newState.member.user.username;
        const now = Date.now();

        // إذا انضم العضو لروم صوتي
        if (!oldState.channelId && newState.channelId) {
            voiceJoinTimes.set(userId, now);
        }
        // إذا خرج العضو من روم صوتي
        else if (oldState.channelId && !newState.channelId) {
            const joinTime = voiceJoinTimes.get(userId);
            if (joinTime) {
                const timeSpent = Math.floor((now - joinTime) / 1000); // بالثواني
                if (timeSpent > 0) {
                    await updateUserStats(userId, username, timeSpent);
                }
                voiceJoinTimes.delete(userId);
            }
        }
        // إذا انتقل من روم لآخر
        else if (oldState.channelId && newState.channelId && oldState.channelId !== newState.channelId) {
            const joinTime = voiceJoinTimes.get(userId);
            if (joinTime) {
                const timeSpent = Math.floor((now - joinTime) / 1000);
                if (timeSpent > 0) {
                    await updateUserStats(userId, username, timeSpent);
                }
            }
            voiceJoinTimes.set(userId, now);
        }
    } catch (error) {
        console.error('خطأ في معالجة تغيير حالة الصوت:', error);
    }
});

// التعامل مع انضمام عضو جديد
client.on('guildMemberAdd', async member => {
    try {
        const verificationStaffRole = member.guild.roles.cache.get(config.roles.verificationStaff);

        const welcomeEmbed = new EmbedBuilder()
            .setTitle(`🎉 Welcome to ${config.serverName}!`)
            .setDescription(`Welcome ${member.user.username}! Please follow these steps to get verified:`)
            .setColor('#00ff00')
            .addFields(
                {
                    name: '📋 Verification Steps:',
                    value: `1️⃣ Go to <#${config.welcomeChannel}>\n2️⃣ Mention ${verificationStaffRole ? verificationStaffRole : '@Verification Staff'}\n3️⃣ Wait for staff response\n4️⃣ Follow staff instructions`,
                    inline: false
                },
                {
                    name: '⚠️ Important:',
                    value: 'Please be patient and wait for verification staff to assist you.',
                    inline: false
                }
            )
            .setThumbnail(member.user.displayAvatarURL())
            .setTimestamp()
            .setFooter({
                text: `${config.serverName} - Verification System`,
                iconURL: member.guild.iconURL() || config.serverLogo
            });

        // إرسال رسالة الترحيب في الخاص
        await member.send({ embeds: [welcomeEmbed] });

    } catch (error) {
        console.error('خطأ في إرسال رسالة الترحيب:', error);
    }
});

// التعامل مع التفاعلات
client.on('interactionCreate', async interaction => {
    if (!interaction.isChatInputCommand()) return;

    if (interaction.commandName === 'verification') {
        // التحقق من رول مسؤولي التحقق قبل معالجة الأمر
        if (!interaction.member.roles.cache.has(config.roles.verificationStaff)) {
            // لا نرسل أي رد - الأمر سيبدو وكأنه لم يحدث شيء
            return;
        }
        await handleVerification(interaction);
    }

    if (interaction.commandName === 'unverify') {
        // التحقق من رول مسؤولي التحقق قبل معالجة الأمر
        if (!interaction.member.roles.cache.has(config.roles.verificationStaff)) {
            // لا نرسل أي رد - الأمر سيبدو وكأنه لم يحدث شيء
            return;
        }
        await handleUnverification(interaction);
    }
});

// دالة التحقق
async function handleVerification(interaction) {
    try {
        // الحصول على البيانات
        const gender = interaction.options.getString('gender');
        const targetUser = interaction.options.getUser('user');
        const member = interaction.guild.members.cache.get(targetUser.id);

        if (!member) {
            return await interaction.reply({
                content: '❌ Member not found in the server',
                ephemeral: true
            });
        }

        await interaction.deferReply({ ephemeral: true });

        // الحصول على الأدوار
        const verifiedRole = interaction.guild.roles.cache.get(config.roles.verified);
        const genderRole = interaction.guild.roles.cache.get(
            gender === 'male' ? config.roles.male : config.roles.female
        );
        const unverifiedRole = interaction.guild.roles.cache.get(config.roles.unverified);

        // التحقق إذا كان العضو متحقق مسبقاً
        if (member.roles.cache.has(config.roles.verified)) {
            let verificationGender = 'Unknown';

            // تحديد الجنس المحقق مسبقاً
            if (member.roles.cache.has(config.roles.male)) {
                verificationGender = 'Male';
            } else if (member.roles.cache.has(config.roles.female)) {
                verificationGender = 'Female';
            }

            return await interaction.editReply({
                content: `❌ Already verified as ${verificationGender}`,
                ephemeral: true
            });
        }

        // التحقق من وجود الأدوار
        if (!verifiedRole) {
            return await interaction.editReply('❌ رول التحقق غير موجود');
        }
        if (!genderRole) {
            return await interaction.editReply('❌ رول الجنس غير موجود');
        }

        // إزالة رول غير المتحقق إذا كان موجود
        if (unverifiedRole && member.roles.cache.has(unverifiedRole.id)) {
            await member.roles.remove(unverifiedRole);
        }

        // إضافة الأدوار الجديدة
        await member.roles.add([verifiedRole, genderRole]);

        // لا نحتاج لتحديث اسم العضو بعد الآن

        // إنشاء رسالة التأكيد (مرئية فقط للمستخدم)
        const confirmEmbed = new EmbedBuilder()
            .setTitle('✅ Verification Completed')
            .setColor('#00ff00')
            .setDescription(`The member has been successfully verified in ${config.serverName}.`)
            .setTimestamp()
            .setFooter({
                text: `${config.serverName} - Verification System`,
                iconURL: interaction.guild.iconURL() || config.serverLogo
            });

        await interaction.editReply({ embeds: [confirmEmbed] });

        // إرسال لوق مفصل في قناة اللوق
        const logChannel = interaction.guild.channels.cache.get(config.verificationLogChannel);
        if (logChannel) {
            // محاولة الحصول على معلومات الدعوة
            let inviteInfo = 'Unknown';
            try {
                const invites = await interaction.guild.invites.fetch();
                const memberInvite = invites.find(invite => invite.uses > 0);
                if (memberInvite && memberInvite.inviter) {
                    inviteInfo = `${memberInvite.inviter.tag} (${memberInvite.code})`;
                }
            } catch (error) {
                console.log('تعذر الحصول على معلومات الدعوة:', error.message);
            }

            const logEmbed = new EmbedBuilder()
                .setTitle(`🔍 ${config.serverName} - Verification Log`)
                .setColor('#00ff00')
                .addFields(
                    { name: '👤 Member', value: `${targetUser} (${targetUser.tag})`, inline: true },
                    { name: '🆔 User ID', value: targetUser.id, inline: true },
                    { name: '⚧️ Gender', value: gender === 'male' ? 'Male' : 'Female', inline: true },
                    { name: '🎭 Roles Added', value: `${verifiedRole}\n${genderRole}`, inline: false },
                    { name: '👮 Verified By', value: `${interaction.user} (${interaction.user.tag})`, inline: true },
                    { name: '📅 Verification Time', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true },
                    { name: '📊 Account Created', value: `<t:${Math.floor(targetUser.createdTimestamp / 1000)}:R>`, inline: true },
                    { name: '🚪 Joined Server', value: `<t:${Math.floor(member.joinedTimestamp / 1000)}:R>`, inline: true },
                    { name: '🔗 Invited By', value: inviteInfo, inline: true }
                )
                .setTimestamp()
                .setThumbnail(targetUser.displayAvatarURL())
                .setFooter({
                    text: `${config.serverName} - Verification System`,
                    iconURL: interaction.guild.iconURL() || config.serverLogo
                });

            if (unverifiedRole && member.roles.cache.has(unverifiedRole.id)) {
                logEmbed.addFields({
                    name: '🗑️ Roles Removed',
                    value: `${unverifiedRole}`,
                    inline: false
                });
            }

            try {
                await logChannel.send({ embeds: [logEmbed] });
            } catch (error) {
                console.log('تعذر إرسال اللوق إلى القناة المحددة:', error.message);
            }
        }

        // إرسال رسالة ما بعد التحقق للعضو
        try {
            const postVerificationEmbed = new EmbedBuilder()
                .setTitle(`🎉 Welcome to ${config.serverName}!`)
                .setDescription(`Hello ${targetUser.username}! Your account has been successfully verified in ${config.serverName}. Here are the next steps:`)
                .setColor('#00ff00')
                .addFields(
                    {
                        name: '📋 Next Steps:',
                        value: `1️⃣ Visit <#${config.rulesChannel}> to read the server rules\n2️⃣ Get game roles from <#${config.gamesRolesChannel}>\n3️⃣ Follow announcements in <#${config.announcementsChannel}>`,
                        inline: false
                    },
                    {
                        name: '🎮 Enjoy your stay!',
                        value: `Welcome to ${config.serverName} community! Have fun and follow the rules.`,
                        inline: false
                    }
                )
                .setThumbnail(targetUser.displayAvatarURL())
                .setTimestamp()
                .setFooter({
                    text: `${config.serverName} - Welcome System`,
                    iconURL: interaction.guild.iconURL() || config.serverLogo
                });

            await targetUser.send({ embeds: [postVerificationEmbed] });
        } catch (error) {
            console.log('تعذر إرسال رسالة ما بعد التحقق للعضو');
        }

    } catch (error) {
        console.error('خطأ في عملية التحقق:', error);

        try {
            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({ content: '❌ An error occurred during verification' });
            } else {
                await interaction.reply({ content: '❌ An error occurred during verification', ephemeral: true });
            }
        } catch (replyError) {
            console.error('خطأ في إرسال رسالة الخطأ:', replyError);
        }
    }
}

// دالة إلغاء التحقق
async function handleUnverification(interaction) {
    try {
        // الحصول على البيانات
        const targetUser = interaction.options.getUser('user');
        const member = interaction.guild.members.cache.get(targetUser.id);

        if (!member) {
            return await interaction.reply({
                content: '❌ Member not found in the server',
                ephemeral: true
            });
        }

        await interaction.deferReply({ ephemeral: true });

        // الحصول على الأدوار
        const verifiedRole = interaction.guild.roles.cache.get(config.roles.verified);
        const maleRole = interaction.guild.roles.cache.get(config.roles.male);
        const femaleRole = interaction.guild.roles.cache.get(config.roles.female);
        const unverifiedRole = interaction.guild.roles.cache.get(config.roles.unverified);

        // التحقق إذا كان العضو غير متحقق أصلاً
        if (!member.roles.cache.has(config.roles.verified)) {
            return await interaction.editReply({
                content: `❌ Member is not verified`
            });
        }

        // تحديد الجنس المحقق حالياً
        let currentGender = 'Unknown';
        if (member.roles.cache.has(config.roles.male)) {
            currentGender = 'Male';
        } else if (member.roles.cache.has(config.roles.female)) {
            currentGender = 'Female';
        }

        // إزالة أدوار التحقق
        const rolesToRemove = [];
        if (verifiedRole && member.roles.cache.has(verifiedRole.id)) {
            rolesToRemove.push(verifiedRole);
        }
        if (maleRole && member.roles.cache.has(maleRole.id)) {
            rolesToRemove.push(maleRole);
        }
        if (femaleRole && member.roles.cache.has(femaleRole.id)) {
            rolesToRemove.push(femaleRole);
        }

        if (rolesToRemove.length > 0) {
            await member.roles.remove(rolesToRemove);
        }

        // إضافة رول غير المتحقق إذا كان موجود
        if (unverifiedRole) {
            await member.roles.add(unverifiedRole);
        }

        // إنشاء رسالة التأكيد (مرئية فقط للمستخدم)
        const confirmEmbed = new EmbedBuilder()
            .setTitle('✅ Unverification Completed')
            .setColor('#ff0000')
            .setDescription(`The member has been successfully unverified from ${config.serverName}.`)
            .setTimestamp()
            .setFooter({
                text: `${config.serverName} - Verification System`,
                iconURL: interaction.guild.iconURL() || config.serverLogo
            });

        await interaction.editReply({ embeds: [confirmEmbed] });

        // إرسال لوق مفصل في قناة اللوق
        const logChannel = interaction.guild.channels.cache.get(config.verificationLogChannel);
        if (logChannel) {
            const logEmbed = new EmbedBuilder()
                .setTitle(`🚫 ${config.serverName} - Unverification Log`)
                .setColor('#ff0000')
                .addFields(
                    { name: '👤 Member', value: `${targetUser} (${targetUser.tag})`, inline: true },
                    { name: '🆔 User ID', value: targetUser.id, inline: true },
                    { name: '⚧️ Previous Gender', value: currentGender, inline: true },
                    { name: '🗑️ Roles Removed', value: rolesToRemove.map(role => role.toString()).join('\n') || 'None', inline: false },
                    { name: '👮 Unverified By', value: `${interaction.user} (${interaction.user.tag})`, inline: true },
                    { name: '📅 Unverification Time', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
                )
                .setTimestamp()
                .setThumbnail(targetUser.displayAvatarURL())
                .setFooter({
                    text: `${config.serverName} - Verification System`,
                    iconURL: interaction.guild.iconURL() || config.serverLogo
                });

            if (unverifiedRole) {
                logEmbed.addFields({
                    name: '🎭 Roles Added',
                    value: `${unverifiedRole}`,
                    inline: false
                });
            }

            try {
                await logChannel.send({ embeds: [logEmbed] });
            } catch (error) {
                console.log('تعذر إرسال اللوق إلى القناة المحددة:', error.message);
            }
        }

    } catch (error) {
        console.error('خطأ في عملية إلغاء التحقق:', error);

        try {
            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({ content: '❌ An error occurred during unverification' });
            } else {
                await interaction.reply({ content: '❌ An error occurred during unverification', ephemeral: true });
            }
        } catch (replyError) {
            console.error('خطأ في إرسال رسالة الخطأ:', replyError);
        }
    }
}

// حفظ البيانات عند إغلاق البوت
process.on('SIGINT', async () => {
    console.log('🔄 حفظ بيانات الوقت الصوتي...');

    // حفظ أوقات الأعضاء الحاليين في الرومات الصوتية
    const now = Date.now();
    for (const [userId, joinTime] of voiceJoinTimes.entries()) {
        try {
            const guild = client.guilds.cache.first();
            if (guild) {
                const member = guild.members.cache.get(userId);
                if (member) {
                    const timeSpent = Math.floor((now - joinTime) / 1000);
                    if (timeSpent > 0) {
                        await updateUserStats(userId, member.user.username, timeSpent);
                    }
                }
            }
        } catch (error) {
            console.error('خطأ في حفظ بيانات العضو:', userId, error);
        }
    }

    // إغلاق قاعدة البيانات
    db.close((err) => {
        if (err) {
            console.error('خطأ في إغلاق قاعدة البيانات:', err);
        } else {
            console.log('✅ تم حفظ البيانات وإغلاق قاعدة البيانات');
        }
        process.exit(0);
    });
});

// تسجيل الدخول
client.login(config.token);